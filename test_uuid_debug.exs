#!/usr/bin/env elixir

# Simple debug script to test UUID inference
Code.require_file("test/support/repos.ex")

# Start the repos
{:ok, _} = Drops.Repos.Sqlite.start_link()

# Test the inference
alias Drops.Relation.SQL.Inference
schema = Inference.infer_from_table("uuid_organizations", Drops.Repos.Sqlite)

IO.puts("=== Inferred Schema for uuid_organizations ===")
IO.puts("Source: #{schema.source}")
IO.puts("Primary key fields:")
Enum.each(schema.primary_key.fields, fn field ->
  IO.puts("  #{field.name}: #{inspect(field.ecto_type)} (type: #{inspect(field.type)})")
end)

IO.puts("All fields:")
Enum.each(schema.fields, fn field ->
  IO.puts("  #{field.name}: #{inspect(field.ecto_type)} (type: #{inspect(field.type)})")
end)

# Test field candidates
alias Drops.Relation.Inference
candidates = Inference.build_field_candidates(schema, [], [])

IO.puts("\n=== Field Candidates ===")
Enum.each(candidates.candidates, fn candidate ->
  field = candidate.field
  IO.puts("  #{field.name}: #{inspect(field.ecto_type)} | source: #{candidate.source} | category: #{candidate.category} | placement: #{candidate.placement}")
end)

# Test what field definitions would be generated
field_defs = Drops.Relation.Inference.FieldCandidates.field_definitions(candidates)
IO.puts("\n=== Field Definitions to Generate ===")
Enum.each(field_defs, fn candidate ->
  field = candidate.field
  IO.puts("  #{field.name}: #{inspect(field.ecto_type)} (category: #{candidate.category})")
end)
