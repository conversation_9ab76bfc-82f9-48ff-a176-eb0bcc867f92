#!/usr/bin/env elixir

# Test if the generated composite primary key AST compiles correctly

defmodule TestCompositePK do
  use Ecto.Schema

  @primary_key false
  schema "test_composite" do
    field(:part1, :string, primary_key: true)
    field(:part2, :integer, primary_key: true)
    field(:data, :string)
  end
end

IO.puts("Composite PK schema compiled successfully!")
IO.puts("Primary key: #{inspect(TestCompositePK.__schema__(:primary_key))}")
IO.puts("Fields: #{inspect(TestCompositePK.__schema__(:fields))}")
