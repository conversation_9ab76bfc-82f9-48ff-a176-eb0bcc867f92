defmodule Drops.Relation.Inference do
  alias Drops.Relation.SQL
  alias Drops.Relation.Schema.Field

  defmodule FieldCandidate do
    @moduledoc """
    Represents a field candidate during schema inference.

    This structure helps organize field information and determine where each field
    should be placed in the final schema (primary key attributes, excluded fields,
    regular field definitions, etc.).
    """

    defstruct [
      # The Field struct with all metadata
      :field,
      # :inferred | :custom | :merged
      :source,
      # :primary_key | :foreign_key | :regular | :timestamp | :excluded
      :category,
      # :attribute | :field_definition | :excluded | :timestamps_macro
      :placement
    ]

    @type t :: %__MODULE__{
            field: Field.t(),
            source: :inferred | :custom | :merged,
            category: :primary_key | :foreign_key | :regular | :timestamp | :excluded,
            placement: :attribute | :field_definition | :excluded | :timestamps_macro
          }

    @doc """
    Creates a new FieldCandidate from a Field struct.
    """
    @spec new(Field.t(), atom(), atom()) :: t()
    def new(%Field{} = field, source, category) do
      placement = determine_placement(field, category)

      %__MODULE__{
        field: field,
        source: source,
        category: category,
        placement: placement
      }
    end

    # Determines where a field should be placed in the final schema.
    @spec determine_placement(Field.t(), atom()) :: atom()
    defp determine_placement(%Field{name: name, ecto_type: ecto_type}, category) do
      case category do
        :primary_key ->
          cond do
            # Default Ecto primary key - exclude (Ecto adds automatically)
            name == :id and ecto_type == :id ->
              :excluded

            # Binary ID and UUID primary keys need both @primary_key attribute AND field definition
            ecto_type in [:binary_id, Ecto.UUID] ->
              :field_definition

            # Other custom primary keys need @primary_key attribute only
            true ->
              :attribute
          end

        :composite_primary_key ->
          # Composite primary key fields are defined as regular fields with primary_key: true
          :field_definition

        :timestamp ->
          # Timestamp fields use the timestamps() macro
          :timestamps_macro

        :excluded ->
          # Fields that should not appear in the schema
          :excluded

        _ ->
          # Regular fields and foreign keys are defined as field() calls
          :field_definition
      end
    end

    @doc """
    Categorizes a field based on its characteristics and context.
    """
    @spec categorize_field(Field.t(), [atom()], boolean(), boolean()) :: atom()
    def categorize_field(
          %Field{name: name},
          primary_key_names,
          is_association_fk,
          is_composite_pk \\ false
        ) do
      cond do
        name in primary_key_names and is_composite_pk ->
          :composite_primary_key

        name in primary_key_names ->
          :primary_key

        name in [:inserted_at, :updated_at] ->
          :timestamp

        is_association_fk ->
          :excluded

        foreign_key_field?(name) ->
          :foreign_key

        true ->
          :regular
      end
    end

    @doc """
    Checks if a field name looks like a foreign key field.
    """
    @spec foreign_key_field?(atom()) :: boolean()
    def foreign_key_field?(field_name) when is_atom(field_name) do
      field_name
      |> Atom.to_string()
      |> String.ends_with?("_id")
    end
  end

  defmodule FieldCandidates do
    @moduledoc """
    Collection of field candidates with helper functions for organizing and processing them.
    """

    alias Drops.Relation.Inference.FieldCandidate
    alias Drops.Relation.Schema.Field

    defstruct candidates: []

    @type t :: %__MODULE__{
            candidates: [FieldCandidate.t()]
          }

    @doc """
    Creates a new FieldCandidates collection.
    """
    @spec new([FieldCandidate.t()]) :: t()
    def new(candidates \\ []) do
      %__MODULE__{candidates: candidates}
    end

    @doc """
    Adds a field candidate to the collection.
    """
    @spec add(t(), FieldCandidate.t()) :: t()
    def add(
          %__MODULE__{candidates: candidates} = collection,
          %FieldCandidate{} = candidate
        ) do
      %{collection | candidates: candidates ++ [candidate]}
    end

    @doc """
    Filters candidates by placement type.
    """
    @spec by_placement(t(), atom()) :: [FieldCandidate.t()]
    def by_placement(%__MODULE__{candidates: candidates}, placement) do
      Enum.filter(candidates, &(&1.placement == placement))
    end

    @doc """
    Filters candidates by category.
    """
    @spec by_category(t(), atom()) :: [FieldCandidate.t()]
    def by_category(%__MODULE__{candidates: candidates}, category) do
      Enum.filter(candidates, &(&1.category == category))
    end

    @doc """
    Gets all field definitions that should be generated.
    """
    @spec field_definitions(t()) :: [FieldCandidate.t()]
    def field_definitions(collection) do
      by_placement(collection, :field_definition)
    end

    @doc """
    Gets primary key fields that need @primary_key attribute.
    """
    @spec primary_key_attributes(t()) :: [FieldCandidate.t()]
    def primary_key_attributes(collection) do
      by_placement(collection, :attribute)
    end

    @doc """
    Gets composite primary key fields.
    """
    @spec composite_primary_key_fields(t()) :: [FieldCandidate.t()]
    def composite_primary_key_fields(collection) do
      by_category(collection, :composite_primary_key)
    end

    @doc """
    Checks if timestamps() macro should be used.
    """
    @spec has_timestamps?(t()) :: boolean()
    def has_timestamps?(collection) do
      timestamp_candidates = by_placement(collection, :timestamps_macro)

      has_inserted_at = Enum.any?(timestamp_candidates, &(&1.field.name == :inserted_at))
      has_updated_at = Enum.any?(timestamp_candidates, &(&1.field.name == :updated_at))

      has_inserted_at and has_updated_at
    end

    @doc """
    Gets foreign key type for @foreign_key_type attribute.
    """
    @spec foreign_key_type(t()) :: atom() | nil
    def foreign_key_type(collection) do
      fk_candidates = by_category(collection, :foreign_key)

      # Check if there are any binary_id or Ecto.UUID foreign key fields
      has_binary_id_fks =
        Enum.any?(fk_candidates, fn candidate ->
          candidate.field.ecto_type in [:binary_id, Ecto.UUID]
        end)

      if has_binary_id_fks, do: :binary_id, else: nil
    end
  end

  @doc """
  Builds field candidates from inferred schema, associations, and custom fields.
  """
  @spec build_field_candidates(
          Drops.Relation.Schema.t(),
          list(),
          list()
        ) :: FieldCandidates.t()
  def build_field_candidates(drops_schema, association_definitions, custom_fields) do
    # Get primary key field names
    primary_key_names = Enum.map(drops_schema.primary_key.fields, & &1.name)

    # Check if this is a composite primary key
    is_composite_pk = length(primary_key_names) > 1

    # Get field names that should be excluded due to associations
    association_field_names = extract_association_field_names(association_definitions)

    # Process custom fields into Field structs
    custom_field_structs = process_custom_fields(custom_fields)
    custom_field_map = Map.new(custom_field_structs, &{&1.name, &1})

    # Start with empty collection
    candidates = FieldCandidates.new()

    # Process inferred fields
    candidates =
      Enum.reduce(drops_schema.fields, candidates, fn inferred_field, acc ->
        # Check if this field has a custom override
        case Map.get(custom_field_map, inferred_field.name) do
          nil ->
            # Pure inferred field
            is_association_fk = inferred_field.name in association_field_names

            category =
              FieldCandidate.categorize_field(
                inferred_field,
                primary_key_names,
                is_association_fk,
                is_composite_pk
              )

            candidate = FieldCandidate.new(inferred_field, :inferred, category)
            FieldCandidates.add(acc, candidate)

          custom_field ->
            # Merged field (inferred + custom)
            merged_field = Field.merge(inferred_field, custom_field)
            is_association_fk = merged_field.name in association_field_names

            category =
              FieldCandidate.categorize_field(
                merged_field,
                primary_key_names,
                is_association_fk,
                is_composite_pk
              )

            candidate = FieldCandidate.new(merged_field, :merged, category)
            FieldCandidates.add(acc, candidate)
        end
      end)

    # Add any custom fields that don't have corresponding inferred fields
    inferred_field_names = MapSet.new(drops_schema.fields, & &1.name)

    additional_custom_fields =
      Enum.reject(custom_field_structs, fn field ->
        MapSet.member?(inferred_field_names, field.name)
      end)

    Enum.reduce(additional_custom_fields, candidates, fn custom_field, acc ->
      is_association_fk = custom_field.name in association_field_names

      category =
        FieldCandidate.categorize_field(
          custom_field,
          primary_key_names,
          is_association_fk,
          is_composite_pk
        )

      candidate = FieldCandidate.new(custom_field, :custom, category)
      FieldCandidates.add(acc, candidate)
    end)
  end

  @doc """
  Generates Ecto schema AST from field candidates.
  """
  @spec generate_schema_ast_from_candidates(
          Drops.Relation.Schema.t(),
          list(),
          list(),
          String.t()
        ) :: Macro.t()
  def generate_schema_ast_from_candidates(
        drops_schema,
        association_definitions,
        custom_fields,
        table_name
      ) do
    # Build field candidates
    candidates =
      build_field_candidates(drops_schema, association_definitions, custom_fields)

    # Generate field definitions from candidates that should be field() calls
    # Sort by the original field order from the drops_schema to maintain consistent ordering
    field_definitions =
      candidates
      |> FieldCandidates.field_definitions()
      |> Enum.sort_by(fn candidate ->
        # Find the index of this field in the original schema fields
        Enum.find_index(drops_schema.fields, &(&1.name == candidate.field.name)) || 999
      end)
      |> Enum.map(&generate_field_definition_from_candidate/1)

    # Add timestamps() macro if needed
    all_field_definitions =
      if FieldCandidates.has_timestamps?(candidates) do
        field_definitions ++ [quote(do: timestamps())]
      else
        field_definitions
      end

    # Generate @primary_key attribute if needed
    primary_key_attr =
      generate_primary_key_attribute_from_candidates(candidates, drops_schema.primary_key)

    # Generate @foreign_key_type attribute if needed
    foreign_key_type_attr =
      generate_foreign_key_type_attribute_from_candidates(candidates)

    # Create the final schema AST with attributes
    schema_ast =
      quote location: :keep do
        schema unquote(table_name) do
          (unquote_splicing(all_field_definitions))

          unquote(association_definitions)
        end
      end

    # Combine attributes with schema
    attributes = []

    attributes =
      if primary_key_attr != nil do
        [primary_key_attr | attributes]
      else
        attributes
      end

    attributes =
      if foreign_key_type_attr != nil do
        [foreign_key_type_attr | attributes]
      else
        attributes
      end

    if length(attributes) > 0 do
      quote location: :keep do
        (unquote_splicing(Enum.reverse(attributes)))
        unquote(schema_ast)
      end
    else
      schema_ast
    end
  end

  @doc """
  Generates a field definition AST from a field candidate.
  """
  @spec generate_field_definition_from_candidate(FieldCandidate.t()) :: Macro.t()
  def generate_field_definition_from_candidate(%FieldCandidate{
        field: field,
        category: category
      }) do
    # Handle parameterized types by extracting options
    {type, type_opts} = extract_type_and_options(field.ecto_type)

    base_opts = if field.source != field.name, do: [source: field.source], else: []

    # Add primary_key: true for composite primary key fields and binary_id/UUID primary keys
    pk_opts =
      cond do
        category == :composite_primary_key ->
          [primary_key: true]

        category == :primary_key and field.ecto_type in [:binary_id, Ecto.UUID] ->
          [primary_key: true]

        true ->
          []
      end

    all_opts = Keyword.merge(type_opts, base_opts) |> Keyword.merge(pk_opts)

    if all_opts == [] do
      quote do
        Ecto.Schema.field(unquote(field.name), unquote(type))
      end
    else
      quote do
        Ecto.Schema.field(unquote(field.name), unquote(type), unquote(all_opts))
      end
    end
  end

  @doc """
  Generates @primary_key attribute from field candidates.
  """
  @spec generate_primary_key_attribute_from_candidates(
          FieldCandidates.t(),
          Drops.Relation.Schema.PrimaryKey.t()
        ) :: Macro.t() | nil
  def generate_primary_key_attribute_from_candidates(candidates, _primary_key) do
    # Get primary key candidates that need @primary_key attribute (single custom PKs)
    pk_attribute_candidates = FieldCandidates.primary_key_attributes(candidates)

    # Get composite primary key candidates
    composite_pk_candidates = FieldCandidates.composite_primary_key_fields(candidates)

    # Get binary_id/UUID primary key candidates that are defined as fields
    binary_id_pk_candidates =
      candidates
      |> FieldCandidates.by_category(:primary_key)
      |> Enum.filter(fn candidate ->
        candidate.placement == :field_definition and
          candidate.field.ecto_type in [:binary_id, Ecto.UUID]
      end)

    cond do
      length(composite_pk_candidates) > 0 ->
        # Composite primary key - use @primary_key false (fields have primary_key: true)
        {:@, [], [{:primary_key, [], [false]}]}

      length(binary_id_pk_candidates) == 1 ->
        # Binary ID/UUID primary key defined as field - still needs @primary_key attribute
        [candidate] = binary_id_pk_candidates
        field = candidate.field

        # Determine if autogenerate should be used
        autogenerate = should_autogenerate?(field.ecto_type)

        if autogenerate do
          {:@, [],
           [
             {:primary_key, [],
              [{:{}, [], [field.name, field.ecto_type, [autogenerate: true]]}]}
           ]}
        else
          {:@, [], [{:primary_key, [], [{:{}, [], [field.name, field.ecto_type]}]}]}
        end

      length(pk_attribute_candidates) == 1 ->
        # Single field primary key
        [candidate] = pk_attribute_candidates
        field = candidate.field

        # Determine if autogenerate should be used
        autogenerate = should_autogenerate?(field.ecto_type)

        if autogenerate do
          {:@, [],
           [
             {:primary_key, [],
              [{:{}, [], [field.name, field.ecto_type, [autogenerate: true]]}]}
           ]}
        else
          {:@, [], [{:primary_key, [], [{:{}, [], [field.name, field.ecto_type]}]}]}
        end

      true ->
        # No custom primary key attribute needed
        nil
    end
  end

  @doc """
  Generates @foreign_key_type attribute from field candidates.
  """
  @spec generate_foreign_key_type_attribute_from_candidates(FieldCandidates.t()) ::
          Macro.t() | nil
  def generate_foreign_key_type_attribute_from_candidates(candidates) do
    case FieldCandidates.foreign_key_type(candidates) do
      :binary_id ->
        {:@, [], [{:foreign_key_type, [], [:binary_id]}]}

      _ ->
        nil
    end
  end

  # Determines if a primary key field should use autogenerate
  @spec should_autogenerate?(atom()) :: boolean()
  defp should_autogenerate?(ecto_type) do
    case ecto_type do
      :id -> true
      :binary_id -> true
      Ecto.UUID -> true
      _ -> false
    end
  end

  def infer_schema(relation, name, repo) do
    # Use the unified schema inference implementation
    drops_schema = SQL.Inference.infer_from_table(name, repo)

    # Get optional Ecto associations definitions AST
    association_definitions = Module.get_attribute(relation, :associations, [])

    # Generate the Ecto schema AST using the new field candidate approach
    ecto_schema_ast =
      generate_schema_ast_from_candidates(
        drops_schema,
        association_definitions,
        # No custom fields for this function
        [],
        name
      )

    {ecto_schema_ast, drops_schema}
  end

  def infer_schema_fields_only(_relation, name, repo) do
    # Use the unified schema inference implementation
    # Return only the Drops.Relation.Schema - no more Ecto AST caching
    SQL.Inference.infer_from_table(name, repo)
  end

  # Legacy function - now delegates to the new field candidate approach
  def combine_schema_with_associations_and_custom_fields(
        drops_schema,
        association_definitions,
        custom_fields,
        table_name
      ) do
    generate_schema_ast_from_candidates(
      drops_schema,
      association_definitions,
      custom_fields,
      table_name
    )
  end

  # Simplified custom field processing using Macro.expand on each field element
  defp process_custom_fields(custom_fields) do
    expanded_fields = Enum.map(custom_fields, &Macro.expand(&1, __ENV__))

    Enum.map(expanded_fields, fn {name, type, opts} ->
      source = Keyword.get(opts, :source, name)

      meta = %{
        is_nullable: Keyword.get(opts, :null),
        default: Keyword.get(opts, :default),
        check_constraints: []
      }

      ecto_type = build_ecto_type(type, opts)
      normalized_type = normalize_type(ecto_type)

      Field.new(name, normalized_type, ecto_type, source, meta)
    end)
  end

  # Simplified ecto_type building (now works with expanded types)
  defp build_ecto_type(type, opts) do
    if length(opts) > 0, do: {type, opts}, else: type
  end

  # Simplified type normalization
  defp normalize_type(type) do
    case type do
      :string ->
        :string

      :integer ->
        :integer

      :boolean ->
        :boolean

      :float ->
        :float

      :decimal ->
        :decimal

      :date ->
        :date

      :time ->
        :time

      :naive_datetime ->
        :naive_datetime

      :utc_datetime ->
        :utc_datetime

      :binary ->
        :binary

      :id ->
        :integer

      :binary_id ->
        :binary

      # Default for parameterized types
      {module, _opts} when is_atom(module) ->
        :string

      {container, inner_type} when is_atom(container) ->
        {container, normalize_type(inner_type)}

      # Default fallback
      _ ->
        :string
    end
  end

  # Helper function to extract type and options from ecto_type for field generation
  defp extract_type_and_options(ecto_type) do
    case ecto_type do
      {type, opts} when is_list(opts) ->
        {type, opts}

      {type, opts} when is_map(opts) ->
        {type, Map.to_list(opts)}

      type ->
        {type, []}
    end
  end

  # Helper function to extract foreign key field names from association definitions
  # that should be excluded from inferred schema. For belongs_to associations,
  # Ecto automatically creates the foreign key field UNLESS define_field: false
  # is specified, in which case the user is responsible for defining the field.
  defp extract_association_field_names(association_definitions) do
    case association_definitions do
      # Handle single belongs_to association with options
      {:belongs_to, _meta, [field_name, _related_module, opts]} when is_list(opts) ->
        # Only exclude the foreign key field if define_field is NOT false
        # (i.e., when Ecto will create it automatically)
        if Keyword.get(opts, :define_field, true) == false do
          # User specified define_field: false, so they'll define the field themselves
          # Don't exclude it from inferred schema
          []
        else
          # Ecto will create the field automatically, so exclude it from inferred schema
          [infer_foreign_key_field_name(field_name, opts)]
        end

      {:belongs_to, _meta, [field_name, _related_module]} ->
        # No define_field option specified, defaults to true, so Ecto will create the field
        [infer_foreign_key_field_name(field_name, [])]

      # Handle block with multiple associations
      {:__block__, _meta, associations} when is_list(associations) ->
        Enum.flat_map(associations, &extract_association_field_names/1)

      # Handle other association types (has_many, many_to_many, etc.)
      {assoc_type, _meta, _args} when assoc_type in [:has_many, :has_one, :many_to_many] ->
        # These don't create foreign key fields in the current table
        []

      # Handle any other case
      _ ->
        []
    end
  end

  # Helper function to infer the foreign key field name from association name and options
  defp infer_foreign_key_field_name(association_name, opts) do
    case Keyword.get(opts, :foreign_key) do
      nil ->
        # Default foreign key naming: association_name + "_id"
        String.to_atom("#{association_name}_id")

      foreign_key when is_atom(foreign_key) ->
        foreign_key
    end
  end
end
