defmodule Drops.Relation.SQL.Inference do
  @moduledoc """
  Unified schema inference implementation for database table introspection.

  This module consolidates all schema inference logic into a single, reusable
  implementation that can be used by both runtime schema inference (for dynamic
  relation modules) and code generation (for explicit relation files).

  The module provides a single source of truth for:
  - Database table introspection
  - Type conversion from database types to Ecto types
  - Field metadata extraction
  - Primary key detection
  - Index extraction
  - Schema struct creation

  ## Usage

      # Create schema from database table
      schema = Drops.Relation.SQL.Inference.infer_from_table("users", MyApp.Repo)

      # Create schema with custom options
      schema = Drops.Relation.SQL.Inference.infer_from_table("users", MyApp.Repo,
        include_indices: true,
        include_timestamps: false
      )
  """

  alias Drops.Relation.Schema
  alias Drops.Relation.Schema.{Field, PrimaryKey, Indices}
  alias Drops.Relation.SQL.Introspector

  require Logger

  @doc """
  Infers a complete Drops.Relation.Schema from a database table.

  This is the main entry point for schema inference. It performs database
  introspection and creates a complete Schema struct with all metadata.

  ## Parameters

  - `table_name` - The database table name to introspect
  - `repo` - The Ecto repository module for database access
  - `opts` - Optional configuration (see options below)

  ## Options

  - `:include_indices` - Whether to extract index information (default: true)
  - `:include_timestamps` - Whether to include timestamp fields (default: true)
  - `:default_primary_key` - Default primary key when none found (default: [:id])

  ## Returns

  A `Drops.Relation.Schema.t()` struct containing all inferred metadata.

  ## Examples

      iex> schema = Drops.Relation.SQL.Inference.infer_from_table("users", MyApp.Repo)
      iex> schema.source
      "users"
      iex> length(schema.fields)
      5
  """
  @spec infer_from_table(String.t(), module(), keyword()) :: Schema.t()
  def infer_from_table(table_name, repo, opts \\ []) do
    include_indices = Keyword.get(opts, :include_indices, true)
    include_timestamps = Keyword.get(opts, :include_timestamps, true)
    default_primary_key = Keyword.get(opts, :default_primary_key, [:id])

    # Introspect table columns and types using the new Introspector
    columns = Introspector.introspect_table_columns(repo, table_name)

    # Create the Drops.Relation.Schema from the introspected data
    create_schema_from_columns(
      table_name,
      columns,
      repo,
      include_indices: include_indices,
      include_timestamps: include_timestamps,
      default_primary_key: default_primary_key
    )
  end

  @doc """
  Normalizes Ecto types to their base types.

  ## Parameters

  - `ecto_type` - The Ecto type to normalize

  ## Returns

  The normalized Ecto type.

  ## Examples

      iex> Drops.Relation.SQL.Inference.normalize_ecto_type(:id)
      :integer
      iex> Drops.Relation.SQL.Inference.normalize_ecto_type(:string)
      :string
  """
  @spec normalize_ecto_type(atom() | tuple()) :: atom() | tuple()
  def normalize_ecto_type(ecto_type) do
    case ecto_type do
      :id -> :integer
      :binary_id -> :binary
      Ecto.UUID -> :binary
      {:array, inner_type} -> {:array, normalize_ecto_type(inner_type)}
      other -> other
    end
  end

  # Private helper functions

  # Creates a Drops.Relation.Schema from introspected column data
  defp create_schema_from_columns(table_name, columns, repo, opts) do
    include_indices = Keyword.get(opts, :include_indices, true)
    include_timestamps = Keyword.get(opts, :include_timestamps, false)
    default_primary_key = Keyword.get(opts, :default_primary_key, [:id])

    # Extract primary key fields from columns
    primary_key_fields =
      columns
      |> Enum.filter(& &1.primary_key)
      |> Enum.map(&String.to_atom(&1.name))
      |> case do
        # Use default primary key when none found
        [] -> default_primary_key
        fields -> fields
      end

    # Convert columns to Field structs
    fields =
      columns
      |> maybe_filter_timestamps(include_timestamps)
      |> maybe_filter_default_id_primary_key()
      |> Enum.map(&create_field_from_column(&1, repo))

    # Add the default :id field that Ecto adds (if not already present)
    all_fields = maybe_add_default_id_field(fields, columns)

    # Create primary key with proper Field structs
    primary_key_field_structs =
      Enum.filter(all_fields, fn field ->
        field.name in primary_key_fields
      end)

    primary_key = PrimaryKey.new(primary_key_field_structs)

    # Extract indices from database if requested
    indices =
      if include_indices and repo do
        case extract_indices_from_db(repo, table_name) do
          {:ok, indices} -> indices
          {:error, _} -> Indices.new()
        end
      else
        Indices.new()
      end

    # Create the schema struct (without associations as requested)
    Schema.new(
      table_name,
      primary_key,
      # foreign_keys - cannot be inferred from database structure alone
      [],
      all_fields,
      indices,
      # virtual_fields - cannot be inferred from database structure alone
      []
    )
  end

  # Filters out timestamp fields if not included
  defp maybe_filter_timestamps(columns, true), do: columns

  defp maybe_filter_timestamps(columns, false) do
    Enum.reject(columns, fn column ->
      column.name in ["inserted_at", "updated_at"]
    end)
  end

  # Filters out the default 'id' primary key field (Ecto adds this automatically)
  # Only filters out integer id primary keys, not UUID ones since UUID primary keys
  # are explicitly defined in migrations and should be preserved
  defp maybe_filter_default_id_primary_key(columns) do
    Enum.reject(columns, fn column ->
      column.primary_key and
        column.name == "id" and
        String.upcase(column.type) == "INTEGER"
    end)
  end

  # Creates a Field struct from a column map
  defp create_field_from_column(column, repo) do
    field_name = String.to_atom(column.name)
    base_ecto_type = Introspector.db_type_to_ecto_type(repo, column.type, column.name)

    # Use :id type for primary key columns and foreign key columns
    ecto_type =
      cond do
        column.primary_key and base_ecto_type == :integer ->
          :id

        column.primary_key and base_ecto_type == :binary_id ->
          :binary_id

        # Handle Ecto.UUID type for SQLite UUID primary keys
        column.primary_key and base_ecto_type == Ecto.UUID ->
          Ecto.UUID

        foreign_key_field?(column.name) and base_ecto_type == :integer ->
          :id

        foreign_key_field?(column.name) and base_ecto_type == :binary_id ->
          :binary_id

        # Handle Ecto.UUID type for SQLite UUID foreign keys
        foreign_key_field?(column.name) and base_ecto_type == Ecto.UUID ->
          Ecto.UUID

        true ->
          base_ecto_type
      end

    # Extract metadata from column introspection
    meta = %{
      is_nullable: Map.get(column, :is_nullable),
      default: Map.get(column, :default),
      check_constraints: Map.get(column, :check_constraints, [])
    }

    Field.new(
      field_name,
      normalize_ecto_type(ecto_type),
      ecto_type,
      field_name,
      meta
    )
  end

  # Checks if a field name looks like a foreign key field
  defp foreign_key_field?(field_name) when is_binary(field_name) do
    String.ends_with?(field_name, "_id")
  end

  # Adds the default :id field that Ecto adds if not already present
  defp maybe_add_default_id_field(fields, _columns) do
    # Check if an id field already exists in the processed fields
    has_processed_id_field = Enum.any?(fields, &(&1.name == :id))

    if has_processed_id_field do
      # If id field already exists, don't add another one
      fields
    else
      # Add a default integer id field only if no id field exists
      # This preserves the original behavior for tables without explicit id columns
      id_field = Field.new(:id, :integer, :id, :id)
      [id_field | fields]
    end
  end

  # Extracts indices from database
  defp extract_indices_from_db(repo, table_name) do
    try do
      # Use the new Introspector module
      Introspector.get_table_indices(repo, table_name)
    rescue
      _ -> {:error, :introspection_failed}
    end
  end
end
