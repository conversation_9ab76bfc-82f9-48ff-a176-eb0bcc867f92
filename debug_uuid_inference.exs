#!/usr/bin/env elixir

# Debug script to check UUID inference
Mix.install([
  {:ecto, "~> 3.13"},
  {:ecto_sql, "~> 3.13"},
  {:ecto_sqlite3, "~> 0.17"}
])

# Load the drops application modules
Code.require_file("lib/drops/relation/sql/introspector.ex")
Code.require_file("lib/drops/relation/sql/introspector/database.ex")
Code.require_file("lib/drops/relation/sql/introspector/database/sqlite.ex")
Code.require_file("lib/drops/relation/sql/inference.ex")
Code.require_file("lib/drops/relation/schema.ex")
Code.require_file("lib/drops/relation/schema/field.ex")
Code.require_file("lib/drops/relation/schema/primary_key.ex")
Code.require_file("lib/drops/relation/schema/indices.ex")
Code.require_file("lib/drops/relation/inference.ex")

# Set up a simple repo for testing
defmodule TestRepo do
  use Ecto.Repo,
    otp_app: :test_app,
    adapter: Ecto.Adapters.SQLite3
end

# Configure the repo
Application.put_env(:test_app, TestRepo,
  database: ":memory:",
  pool: Ecto.Adapters.SQL.Sandbox
)

# Start the repo
{:ok, _} = TestRepo.start_link()

# Create the uuid_organizations table
TestRepo.query!("""
CREATE TABLE uuid_organizations (
  id UUID PRIMARY KEY,
  name TEXT,
  inserted_at TEXT NOT NULL,
  updated_at TEXT NOT NULL
)
""")

# Test introspection
alias Drops.Relation.SQL.Introspector
columns = Introspector.introspect_table_columns(TestRepo, "uuid_organizations")

IO.puts("=== Columns for uuid_organizations ===")
Enum.each(columns, fn column ->
  IO.puts("  #{column.name}: #{column.type} (primary_key: #{column.primary_key})")
  
  # Check what the introspector converts this to
  ecto_type = Introspector.db_type_to_ecto_type(TestRepo, column.type, column.name)
  IO.puts("    -> ecto_type: #{inspect(ecto_type)}")
end)

# Test schema inference
alias Drops.Relation.SQL.Inference
schema = Inference.infer_from_table("uuid_organizations", TestRepo)

IO.puts("\n=== Inferred Schema ===")
IO.puts("Source: #{schema.source}")
IO.puts("Primary key fields: #{inspect(Enum.map(schema.primary_key.fields, &{&1.name, &1.ecto_type}))}")
IO.puts("All fields:")
Enum.each(schema.fields, fn field ->
  IO.puts("  #{field.name}: #{inspect(field.ecto_type)} (type: #{inspect(field.type)})")
end)

# Test field candidates
alias Drops.Relation.Inference
candidates = Inference.build_field_candidates(schema, [], [])

IO.puts("\n=== Field Candidates ===")
Enum.each(candidates.candidates, fn candidate ->
  field = candidate.field
  IO.puts("  #{field.name}: #{inspect(field.ecto_type)} | source: #{candidate.source} | category: #{candidate.category} | placement: #{candidate.placement}")
end)
