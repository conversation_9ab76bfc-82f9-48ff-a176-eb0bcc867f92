#!/usr/bin/env elixir

# Debug script to test composite primary key generation

Mix.install([
  {:ecto, "~> 3.13"},
  {:ecto_sql, "~> 3.13"}
])

# Load the drops application
Code.require_file("lib/drops/relation/schema/field.ex")
Code.require_file("lib/drops/relation/schema/primary_key.ex")
Code.require_file("lib/drops/relation/inference.ex")

alias Drops.Relation.Inference
alias Drops.Relation.Schema.Field

# Create a mock drops schema with composite primary key
drops_schema = %{
  source: "composite_pk",
  primary_key: %{
    fields: [
      Field.new(:part1, :string, :string, :part1),
      Field.new(:part2, :integer, :integer, :part2)
    ]
  },
  fields: [
    Field.new(:part1, :string, :string, :part1),
    Field.new(:part2, :integer, :integer, :part2),
    Field.new(:data, :string, :string, :data)
  ]
}

# Test field candidates generation
candidates = Inference.build_field_candidates(drops_schema, [], [])

IO.puts("Field candidates:")
IO.inspect(candidates, pretty: true)

# Test primary key attribute generation
pk_attr = Inference.generate_primary_key_attribute_from_candidates(candidates, drops_schema.primary_key)

IO.puts("\nGenerated primary key attribute:")
IO.puts(pk_attr)

# Test full schema AST generation
schema_ast = Inference.generate_schema_ast_from_candidates(
  drops_schema,
  [],
  [],
  "composite_pk"
)

IO.puts("\nGenerated schema AST:")
IO.puts(Macro.to_string(schema_ast))
