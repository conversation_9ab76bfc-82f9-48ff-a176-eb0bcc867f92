defmodule Drops.Relation.SQL.InferenceUuidTest do
  use Drops.RelationCase, async: true

  alias Drops.Relation.SQL.Inference
  alias Drops.Relation.Schema

  describe "UUID field inference" do
    test "normalize_ecto_type handles binary_id correctly" do
      assert Inference.normalize_ecto_type(:binary_id) == :binary
      assert Inference.normalize_ecto_type(:id) == :integer
      assert Inference.normalize_ecto_type(:string) == :string
      assert Inference.normalize_ecto_type({:array, :binary_id}) == {:array, :binary}
    end

    test "infers UUID primary key correctly for SQLite" do
      # Create a table with UUID primary key (SQLite stores as TEXT but with UUID type hint)
      Ecto.Adapters.SQL.query!(
        Drops.Repos.Sqlite,
        """
        CREATE TABLE test_uuid_pk_sqlite (
          id UUID PRIMARY KEY,
          name TEXT NOT NULL
        )
        """,
        []
      )

      schema = Inference.infer_from_table("test_uuid_pk_sqlite", Drops.Repos.Sqlite)

      assert %Schema{} = schema
      assert schema.source == "test_uuid_pk_sqlite"
      assert length(schema.primary_key.fields) == 1

      # The id field should be detected as Ecto.UUID for SQLite
      pk_field = hd(schema.primary_key.fields)
      assert pk_field.name == :id
      assert pk_field.ecto_type == Ecto.UUID
      assert pk_field.type == :binary

      # Verify it's in the fields list too
      id_field = Enum.find(schema.fields, &(&1.name == :id))
      assert id_field
      assert id_field.ecto_type == Ecto.UUID
    end

    test "infers UUID primary key correctly for existing uuid_organizations table" do
      # Test the actual uuid_organizations table that was created by migration
      schema = Inference.infer_from_table("uuid_organizations", Drops.Repos.Sqlite)

      assert %Schema{} = schema
      assert schema.source == "uuid_organizations"
      assert length(schema.primary_key.fields) == 1

      # The id field should be detected as Ecto.UUID for SQLite
      pk_field = hd(schema.primary_key.fields)
      assert pk_field.name == :id
      assert pk_field.ecto_type == Ecto.UUID
      assert pk_field.type == :binary

      # Verify it's in the fields list too
      id_field = Enum.find(schema.fields, &(&1.name == :id))
      assert id_field
      assert id_field.ecto_type == Ecto.UUID
    end

    @tag adapter: :postgres
    test "infers UUID primary key correctly for PostgreSQL" do
      # Create a table with UUID primary key (PostgreSQL native UUID type)
      Ecto.Adapters.SQL.query!(
        Drops.Repos.Postgres,
        """
        CREATE TABLE test_uuid_pk_postgres (
          id UUID PRIMARY KEY,
          name TEXT NOT NULL
        )
        """,
        []
      )

      schema = Inference.infer_from_table("test_uuid_pk_postgres", Drops.Repos.Postgres)

      assert %Schema{} = schema
      assert schema.source == "test_uuid_pk_postgres"
      assert length(schema.primary_key.fields) == 1

      # The id field should be detected as binary_id
      pk_field = hd(schema.primary_key.fields)
      assert pk_field.name == :id
      assert pk_field.ecto_type == :binary_id
      assert pk_field.type == :binary

      # Verify it's in the fields list too
      id_field = Enum.find(schema.fields, &(&1.name == :id))
      assert id_field
      assert id_field.ecto_type == :binary_id
    end

    test "infers UUID foreign keys correctly for SQLite" do
      # Create tables with UUID foreign key relationships
      Ecto.Adapters.SQL.query!(
        Drops.Repos.Sqlite,
        """
        CREATE TABLE test_uuid_parent_sqlite (
          id UUID PRIMARY KEY,
          name TEXT NOT NULL
        )
        """,
        []
      )

      Ecto.Adapters.SQL.query!(
        Drops.Repos.Sqlite,
        """
        CREATE TABLE test_uuid_child_sqlite (
          id UUID PRIMARY KEY,
          parent_id UUID NOT NULL,
          title TEXT NOT NULL
        )
        """,
        []
      )

      schema = Inference.infer_from_table("test_uuid_child_sqlite", Drops.Repos.Sqlite)

      assert %Schema{} = schema
      assert schema.source == "test_uuid_child_sqlite"

      # Check primary key
      pk_field = hd(schema.primary_key.fields)
      assert pk_field.name == :id
      assert pk_field.ecto_type == Ecto.UUID

      # Check foreign key field
      parent_id_field = Enum.find(schema.fields, &(&1.name == :parent_id))
      assert parent_id_field
      assert parent_id_field.ecto_type == Ecto.UUID
    end

    @tag adapter: :postgres
    test "infers UUID foreign keys correctly for PostgreSQL" do
      # Create tables with UUID foreign key relationships
      Ecto.Adapters.SQL.query!(
        Drops.Repos.Postgres,
        """
        CREATE TABLE test_uuid_parent_postgres (
          id UUID PRIMARY KEY,
          name TEXT NOT NULL
        )
        """,
        []
      )

      Ecto.Adapters.SQL.query!(
        Drops.Repos.Postgres,
        """
        CREATE TABLE test_uuid_child_postgres (
          id UUID PRIMARY KEY,
          parent_id UUID NOT NULL,
          title TEXT NOT NULL
        )
        """,
        []
      )

      schema =
        Inference.infer_from_table("test_uuid_child_postgres", Drops.Repos.Postgres)

      assert %Schema{} = schema
      assert schema.source == "test_uuid_child_postgres"

      # Check primary key
      pk_field = hd(schema.primary_key.fields)
      assert pk_field.name == :id
      assert pk_field.ecto_type == :binary_id

      # Check foreign key field
      parent_id_field = Enum.find(schema.fields, &(&1.name == :parent_id))
      assert parent_id_field
      assert parent_id_field.ecto_type == :binary_id
    end

    test "handles mixed UUID and integer fields correctly" do
      # Create a table with both UUID and integer fields
      Ecto.Adapters.SQL.query!(
        Drops.Repos.Sqlite,
        """
        CREATE TABLE test_mixed_types_sqlite (
          id UUID PRIMARY KEY,
          sequence_number INTEGER NOT NULL,
          legacy_id INTEGER,
          name TEXT NOT NULL
        )
        """,
        []
      )

      schema = Inference.infer_from_table("test_mixed_types_sqlite", Drops.Repos.Sqlite)

      assert %Schema{} = schema

      # UUID primary key
      pk_field = hd(schema.primary_key.fields)
      assert pk_field.name == :id
      assert pk_field.ecto_type == Ecto.UUID

      # Integer fields
      sequence_field = Enum.find(schema.fields, &(&1.name == :sequence_number))
      assert sequence_field
      assert sequence_field.ecto_type == :integer

      legacy_id_field = Enum.find(schema.fields, &(&1.name == :legacy_id))
      assert legacy_id_field

      # This will be :id since it ends with "_id" and our heuristic treats it as a foreign key
      assert legacy_id_field.ecto_type == :id

      # Text field
      name_field = Enum.find(schema.fields, &(&1.name == :name))
      assert name_field
      assert name_field.ecto_type == :string
    end
  end

  describe "schema generation with UUID" do
    test "generates correct attributes for UUID schema" do
      alias Drops.Relation.Schema.Generator
      alias Drops.Relation.Schema.{Field, PrimaryKey}

      # Create a schema with UUID primary key and foreign keys
      schema = %Schema{
        source: "uuid_test_table",
        primary_key: %PrimaryKey{fields: [Field.new(:id, :binary, :binary_id, :id)]},
        fields: [
          Field.new(:id, :binary, :binary_id, :id),
          Field.new(:name, :string, :string, :name),
          Field.new(:parent_id, :binary, :binary_id, :parent_id)
        ],
        foreign_keys: [],
        indices: %Drops.Relation.Schema.Indices{indices: []},
        virtual_fields: []
      }

      result =
        Generator.generate_module_content("MyApp.UuidTest", "uuid_test_table", schema)

      # Should generate both @primary_key and @foreign_key_type attributes
      assert result =~ "@primary_key {:id, :binary_id, autogenerate: true}"
      assert result =~ "@foreign_key_type :binary_id"

      # Should include the foreign key field
      assert result =~ "field :parent_id, :binary_id"

      # Should exclude the primary key field from field definitions
      refute result =~ "field :id"
    end
  end
end
